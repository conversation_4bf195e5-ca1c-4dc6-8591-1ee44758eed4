import React from 'react';
import { Layout, LocaleProvider } from 'antd';
import DocumentTitle from 'react-document-title';
import { connect } from 'dva';
import { ContainerQuery } from 'react-container-query';
import classNames from 'classnames';
import zhCN from 'antd/lib/locale-provider/zh_CN';
import Header from './Header';
import Context from './MenuContext';
import SiderMenu from '@/components/SiderMenu';
import Authorized from '@/utils/Authorized';

const { Content } = Layout;

const query = {
  'screen-xs': {
    maxWidth: 575,
  },
  'screen-sm': {
    minWidth: 576,
    maxWidth: 767,
  },
  'screen-md': {
    minWidth: 768,
    maxWidth: 991,
  },
  'screen-lg': {
    minWidth: 992,
    maxWidth: 1199,
  },
  'screen-xl': {
    minWidth: 1200,
    maxWidth: 1599,
  },
  'screen-xxl': {
    minWidth: 1600,
  },
};

class CaseQualityDetailLayout extends React.PureComponent {
  state = {
    isMobile: false,
    menuData: [], // 简化的菜单数据，可以根据需要扩展
  };

  getContext() {
    const { location } = this.props;
    return {
      location,
      breadcrumbNameMap: {},
    };
  }

  getPageTitle = () => '病历质检详情 - FRIDAY口腔业务综合管理平台';

  handleMenuCollapse = collapsed => {
    const { dispatch } = this.props;
    dispatch({
      type: 'global/changeLayoutCollapsed',
      payload: collapsed,
    });
  };

  render() {
    const { children, collapsed, navTheme = 'dark', ...restProps } = this.props;
    const { isMobile, menuData } = this.state;

    const layout = (
      <Layout style={{ minHeight: '100vh', minWidth: '1024px' }}>
        <SiderMenu
          Authorized={Authorized}
          theme={navTheme}
          onCollapse={this.handleMenuCollapse}
          menuData={menuData}
          isMobile={isMobile}
          {...restProps}
        />

        <Layout>
          <Header
            handleMenuCollapse={this.handleMenuCollapse}
            isMobile={isMobile}
            {...restProps}
            customLayoutType="topheader"
          />
          <Content>{children}</Content>
        </Layout>
      </Layout>
    );

    return (
      <LocaleProvider locale={zhCN}>
        <React.Fragment>
          <DocumentTitle title={this.getPageTitle()}>
            <ContainerQuery query={query}>
              {params => (
                <Context.Provider value={this.getContext()}>
                  <div className={classNames(params)}>{layout}</div>
                </Context.Provider>
              )}
            </ContainerQuery>
          </DocumentTitle>
        </React.Fragment>
      </LocaleProvider>
    );
  }
}

export default connect(({ global, setting, login, loading }) => ({
  collapsed: global.collapsed,
  menuCollapsed: global.menuCollapsed,
  layout: setting.layout,
  login,
  ...setting,
  loading,
}))(CaseQualityDetailLayout);
