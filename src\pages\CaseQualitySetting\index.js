/**
 * @Description: 病历质检设置
 * @author: sy
 */
import React, { Component } from 'react';
import { connect } from 'dva';
import { Select, Table, Switch, Button, message } from 'antd';
import router from 'umi/router';

import styles from './index.less';
import backIcon from '@/assets/CaseQualityInspection/back.png';

const { Option } = Select;

@connect(({ loading, caseQualitySetting }) => ({
  loading,
  caseQualitySetting,
}))
class CaseQualitySetting extends Component {
  // localStorage存储键名
  STORAGE_KEY = 'caseQualitySettings';

  constructor(props) {
    super(props);
    this.state = {
      // 基础设置
      evaluationCycle: 'beijing', // 规则城市选择
        // 学科选择
      selectedSubject: 'orthodontics', // 默认选择正畸

      // 所有数据
      data: {
        beijing: {
          scoreConfigData: [
            {
              key: '1',
              category: '病历首页',
              ruleDescription: '未填写病历首页',
              score: 5,
              status: true,
              children: [
                {
                  key: '1-1',
                  category: '',
                  ruleDescription: '病历首页需记录姓名',
                  score: 1,
                  status: true,
                },
                {
                  key: '1-2',
                  category: '',
                  ruleDescription: '病历首页需记录性别',
                  score: 1,
                  status: true,
                },
                {
                  key: '1-3',
                  category: '',
                  ruleDescription: '病历首页需记录出生日期',
                  score: 1,
                  status: true,
                },
                {
                  key: '1-4',
                  category: '',
                  ruleDescription: '病历首页需记录既往病史、家庭史、过敏史',
                  score: 2,
                  status: true,
                },
              ],
            },
            {
              key: '2',
              category: '主诉',
              ruleDescription: '未填写主诉',
              score: 15,
              status: true,
              children: [
                {
                  key: '2-1',
                  category: '',
                  ruleDescription: '目的型主诉，需填写病变部位',
                  score: 5,
                  status: true,
                },
                {
                  key: '2-2',
                  category: '',
                  ruleDescription: '目的型主诉，需填写主要症状',
                  score: 5,
                  status: true,
                },
                {
                  key: '2-3',
                  category: '',
                  ruleDescription: '目的型主诉，需填写发病时间（或病程日期）',
                  score: 5,
                  status: true,
                },
              ],
            },
            {
              key: '3',
              category: '现病史',
              ruleDescription: '未填写现病史',
              score: 15,
              status: true,
              children: [
                {
                  key: '3-1',
                  category: '',
                  ruleDescription: '需记录主诉牙（主诉病）病史的发生',
                  score: 5,
                  status: true,
                },
                {
                  key: '3-2',
                  category: '',
                  ruleDescription: '需记录主诉牙（主诉病）病史的发展',
                  score: 5,
                  status: true,
                },
                {
                  key: '3-3',
                  category: '',
                  ruleDescription: '需记录主诉牙（主诉病）病史的曾经治疗',
                  score: 5,
                  status: true,
                },
              ],
            },
            {
              key: '4',
              category: '既往史',
              ruleDescription: '未填写既往史',
              score: 5,
              status: true,
              children: [
                {
                  key: '4-1',
                  category: '',
                  ruleDescription: '正确记录患者陈述，无特殊病史时，需记录"无特殊病史"',
                  score: 5,
                  status: true,
                },
              ],
            },
            {
              key: '5',
              category: '检查',
              ruleDescription: '未填写检查',
              score: 15,
              status: true,
              children: [
                {
                  key: '5-1',
                  category: '',
                  ruleDescription: '需正确记录主诉牙检查记录',
                  score: 8,
                  status: true,
                },
                {
                  key: '5-2',
                  category: '',
                  ruleDescription: '需正确记录非主诉牙检查记录',
                  score: 7,
                  status: true,
                },
              ],
            },
            {
              key: '6',
              category: '诊断',
              ruleDescription: '未填写诊断',
              score: 15,
              status: true,
              children: [
                {
                  key: '6-1',
                  category: '',
                  ruleDescription: '需正确记录主诉牙诊断',
                  score: 8,
                  status: true,
                },
                {
                  key: '6-2',
                  category: '',
                  ruleDescription: '需正确书写诊断名称',
                  score: 7,
                  status: true,
                },
              ],
            },
            {
              key: '7',
              category: '治疗计划',
              ruleDescription: '未填写治疗计划',
              score: 15,
              status: true,
              children: [
                {
                  key: '7-1',
                  category: '',
                  ruleDescription: '需正确记录主诉牙（主诉病）的治疗计划',
                  score: 8,
                  status: true,
                },
                {
                  key: '7-2',
                  category: '',
                  ruleDescription: '主诉牙治疗计划需有治疗指导原则',
                  score: 7,
                  status: true,
                },
              ],
            },
            {
              key: '8',
              category: '处置',
              ruleDescription: '未填写处置',
              score: 5,
              status: true,
              children: [
                {
                  key: '8-1',
                  category: '',
                  ruleDescription: '详细记录治疗方位的治疗过程',
                  score: 1,
                  status: true,
                },
                {
                  key: '8-2',
                  category: '',
                  ruleDescription: '详细记录治疗操作',
                  score: 1,
                  status: true,
                },
                {
                  key: '8-3',
                  category: '',
                  ruleDescription: '详细记录治疗用药（材料）',
                  score: 1,
                  status: true,
                },
                {
                  key: '8-4',
                  category: '',
                  ruleDescription: '详细记录治疗技术经过',
                  score: 2,
                  status: true,
                },
              ],
            },
            {
              key: '9',
              category: '签名',
              ruleDescription: '未填写签名',
              score: 10,
              status: true,
              children: [
                {
                  key: '9-1',
                  category: '',
                  ruleDescription: '经治医师、指导医师签全名，签名字迹清楚',
                  score: 10,
                  status: true,
                },
              ],
            },
          ],
          redlineRules: {
            orthodontics: [
              {
                key: '1',
                rule: '治疗前后的资料（面像照片和合像照片、曲断片，原始及治疗后模型）必须完整；',
                status: true,
              },
              {
                key: '2',
                rule:
                  '正畸初戴前，签署正畸治疗计划同意书、固定矫治器矫治知情同意书或者无托槽隐形矫治知情同意书；',
                status: true,
              },
              {
                key: '3',
                rule: '结束时，固定矫治需签署拆除固定矫治器同意书',
                status: true,
              },
              {
                key: '4',
                rule: '所有正畸结束都需要签署保持器佩戴注意事项；',
                status: true,
              },
            ],
            planting: [
              {
                key: '1',
                rule: '术前曲断片或CBCT',
                status: true,
              },
              {
                key: '2',
                rule: '知情同意书签署',
                status: true,
              },
              {
                key: '3',
                rule: '术后曲断片或CBCT；',
                status: true,
              },
              {
                key: '4',
                rule: '二期手术前或修复前拍片；',
                status: true,
              },
            ],
            rootCanal: [
              {
                key: '1',
                rule: '需要有术前诊断片',
                status: true,
              },
              {
                key: '2',
                rule: '知情同意书签署',
                status: true,
              },
              {
                key: '3',
                rule: '术中插针片或者术中试尖片',
                status: true,
              },
              {
                key: '4',
                rule: '术后根充片',
                status: true,
              },
            ],
            periodontal: [
              {
                key: '1',
                rule: '术前X片检查',
                status: true,
              },
              {
                key: '2',
                rule: '术前临床检查（牙周大表）',
                status: true,
              },
              {
                key: '3',
                rule: '知情同意书签署',
                status: true,
              },
              {
                key: '4',
                rule: '全口龈下深刮需要术前X片',
                status: true,
              },
            ],
            surgical: [
              {
                key: '1',
                rule: '术前X-ray（埋伏牙拍CBCT）',
                status: true,
              },
              {
                key: '2',
                rule: '签署术前清单',
                status: true,
              },
              {
                key: '3',
                rule: '知情同意书签署',
                status: true,
              },
            ],
            repair: [
              {
                key: '1',
                rule: '术前X-知情同意书签署',
                status: true,
              },
              {
                key: '2',
                rule: '美容修复/复杂修复需要有术前X片',
                status: true,
              },
              {
                key: '3',
                rule: '术前、术后照片（美容修复）',
                status: true,
              },
            ],
          },
        },
         shanghai: {
          scoreConfigData: [
            {
              key: '1',
              category: '病历首页',
              ruleDescription: '未填写病历首页',
              score: 5,
              status: true,
              children: [
                {
                  key: '1-1',
                  category: '',
                  ruleDescription: '病历首页需记录姓名',
                  score: 1,
                  status: true,
                },
                {
                  key: '1-2',
                  category: '',
                  ruleDescription: '病历首页需记录性别',
                  score: 1,
                  status: true,
                },
                {
                  key: '1-3',
                  category: '',
                  ruleDescription: '病历首页需记录出生日期',
                  score: 1,
                  status: true,
                },
                {
                  key: '1-4',
                  category: '',
                  ruleDescription: '病历首页需记录既往病史、家庭史、过敏史11111',
                  score: 2,
                  status: true,
                },
              ],
            },
            {
              key: '2',
              category: '主诉',
              ruleDescription: '未填写主诉',
              score: 15,
              status: true,
              children: [
                {
                  key: '2-1',
                  category: '',
                  ruleDescription: '目的型主诉，需填写病变部位',
                  score: 5,
                  status: true,
                },
                {
                  key: '2-2',
                  category: '',
                  ruleDescription: '目的型主诉，需填写主要症状',
                  score: 5,
                  status: true,
                },
                {
                  key: '2-3',
                  category: '',
                  ruleDescription: '目的型主诉，需填写发病时间（或病程日期）',
                  score: 5,
                  status: true,
                },
              ],
            },
            {
              key: '3',
              category: '现病史',
              ruleDescription: '未填写现病史',
              score: 15,
              status: true,
              children: [
                {
                  key: '3-1',
                  category: '',
                  ruleDescription: '需记录主诉牙（主诉病）病史的发生',
                  score: 5,
                  status: true,
                },
                {
                  key: '3-2',
                  category: '',
                  ruleDescription: '需记录主诉牙（主诉病）病史的发展',
                  score: 5,
                  status: true,
                },
                {
                  key: '3-3',
                  category: '',
                  ruleDescription: '需记录主诉牙（主诉病）病史的曾经治疗',
                  score: 5,
                  status: true,
                },
              ],
            },
            {
              key: '4',
              category: '既往史',
              ruleDescription: '未填写既往史',
              score: 5,
              status: true,
              children: [
                {
                  key: '4-1',
                  category: '',
                  ruleDescription: '正确记录患者陈述，无特殊病史时，需记录"无特殊病史"',
                  score: 5,
                  status: true,
                },
              ],
            },
            {
              key: '5',
              category: '检查',
              ruleDescription: '未填写检查',
              score: 15,
              status: true,
              children: [
                {
                  key: '5-1',
                  category: '',
                  ruleDescription: '需正确记录主诉牙检查记录',
                  score: 8,
                  status: true,
                },
                {
                  key: '5-2',
                  category: '',
                  ruleDescription: '需正确记录非主诉牙检查记录',
                  score: 7,
                  status: true,
                },
              ],
            },
            {
              key: '6',
              category: '诊断',
              ruleDescription: '未填写诊断',
              score: 15,
              status: true,
              children: [
                {
                  key: '6-1',
                  category: '',
                  ruleDescription: '需正确记录主诉牙诊断',
                  score: 8,
                  status: true,
                },
                {
                  key: '6-2',
                  category: '',
                  ruleDescription: '需正确书写诊断名称',
                  score: 7,
                  status: true,
                },
              ],
            },
            {
              key: '7',
              category: '治疗计划',
              ruleDescription: '未填写治疗计划',
              score: 15,
              status: true,
              children: [
                {
                  key: '7-1',
                  category: '',
                  ruleDescription: '需正确记录主诉牙（主诉病）的治疗计划',
                  score: 8,
                  status: true,
                },
                {
                  key: '7-2',
                  category: '',
                  ruleDescription: '主诉牙治疗计划需有治疗指导原则',
                  score: 7,
                  status: true,
                },
              ],
            },
            {
              key: '8',
              category: '处置',
              ruleDescription: '未填写处置',
              score: 5,
              status: true,
              children: [
                {
                  key: '8-1',
                  category: '',
                  ruleDescription: '详细记录治疗方位的治疗过程',
                  score: 1,
                  status: true,
                },
                {
                  key: '8-2',
                  category: '',
                  ruleDescription: '详细记录治疗操作',
                  score: 1,
                  status: true,
                },
                {
                  key: '8-3',
                  category: '',
                  ruleDescription: '详细记录治疗用药（材料）',
                  score: 1,
                  status: true,
                },
                {
                  key: '8-4',
                  category: '',
                  ruleDescription: '详细记录治疗技术经过',
                  score: 2,
                  status: true,
                },
              ],
            },
            {
              key: '9',
              category: '签名',
              ruleDescription: '未填写签名',
              score: 10,
              status: true,
              children: [
                {
                  key: '9-1',
                  category: '',
                  ruleDescription: '经治医师、指导医师签全名，签名字迹清楚',
                  score: 10,
                  status: true,
                },
              ],
            },
          ],
          redlineRules: {
            orthodontics: [
              {
                key: '1',
                rule: '治疗前后的资料（面像照片和合像照片、曲断片，原始及治疗后模型）必须完整；',
                status: true,
              },
              {
                key: '2',
                rule:
                  '正畸初戴前，签署正畸治疗计划同意书、固定矫治器矫治知情同意书或者无托槽隐形矫治知情同意书；',
                status: true,
              },
              {
                key: '3',
                rule: '结束时，固定矫治需签署拆除固定矫治器同意书',
                status: true,
              },
              {
                key: '4',
                rule: '所有正畸结束都需要签署保持器佩戴注意事项；',
                status: true,
              },
            ],
            planting: [
              {
                key: '1',
                rule: '术前曲断片或CBCT',
                status: true,
              },
              {
                key: '2',
                rule: '知情同意书签署',
                status: true,
              },
              {
                key: '3',
                rule: '术后曲断片或CBCT；',
                status: true,
              },
              {
                key: '4',
                rule: '二期手术前或修复前拍片；',
                status: true,
              },
            ],
            rootCanal: [
              {
                key: '1',
                rule: '需要有术前诊断片',
                status: true,
              },
              {
                key: '2',
                rule: '知情同意书签署',
                status: true,
              },
              {
                key: '3',
                rule: '术中插针片或者术中试尖片',
                status: true,
              },
              {
                key: '4',
                rule: '术后根充片',
                status: true,
              },
            ],
            periodontal: [
              {
                key: '1',
                rule: '术前X片检查',
                status: true,
              },
              {
                key: '2',
                rule: '术前临床检查（牙周大表）',
                status: true,
              },
              {
                key: '3',
                rule: '知情同意书签署',
                status: true,
              },
              {
                key: '4',
                rule: '全口龈下深刮需要术前X片',
                status: true,
              },
            ],
            surgical: [
              {
                key: '1',
                rule: '术前X-ray（埋伏牙拍CBCT）',
                status: true,
              },
              {
                key: '2',
                rule: '签署术前清单',
                status: true,
              },
              {
                key: '3',
                rule: '知情同意书签署',
                status: true,
              },
            ],
            repair: [
              {
                key: '1',
                rule: '术前X-知情同意书签署',
                status: true,
              },
              {
                key: '2',
                rule: '美容修复/复杂修复需要有术前X片',
                status: true,
              },
              {
                key: '3',
                rule: '术前、术后照片（美容修复）',
                status: true,
              },
            ],
          },
        },
      },

    

      // 表格展开状态
      expandedRowKeys: ['1'], // 默认展开"病历首页"模块
    };
  }

  componentDidMount() {
    // 初始化数据
    this.getSettingData();
    // 从localStorage恢复设置
    this.loadSettingsFromStorage();
    // 设置默认展开第一项
    this.setDefaultExpanded();
  }

  // 获取设置数据
  getSettingData = () => {
    // 这里可以调用API获取数据
    // const { dispatch } = this.props;
    // dispatch({ type: 'caseQualitySetting/getSettingData' });
  };

  // 设置默认展开第一项
  setDefaultExpanded = () => {
    const { data, evaluationCycle } = this.state;
    const currentData = data[evaluationCycle];

    if (currentData && currentData.scoreConfigData && currentData.scoreConfigData.length > 0) {
      // 找到第一个有子项的父级项目
      const firstParentItem = currentData.scoreConfigData.find(item =>
        item.children && item.children.length > 0
      );

      if (firstParentItem) {
        this.setState({
          expandedRowKeys: [firstParentItem.key],
        });
      }
    }
  };

  // 从localStorage加载设置（优化版）
  loadSettingsFromStorage = () => {
    try {
      const savedSettings = localStorage.getItem(this.STORAGE_KEY);
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);

        // 验证数据结构的完整性
        if (this.validateOptimizedSettingsData(parsedSettings)) {
          // 先设置基础选项（不使用保存的expandedRowKeys，总是默认展开第一项）
          this.setState({
            evaluationCycle: parsedSettings.evaluationCycle || 'beijing',
            selectedSubject: parsedSettings.selectedSubject || 'orthodontics',
          }, () => {
            // 在状态更新后应用变更
            this.applyStoredChanges(parsedSettings);
            // 设置默认展开第一项
            this.setDefaultExpanded();
          });
        }
      }
    } catch (error) {
      // 从localStorage加载设置失败，使用默认设置
    }
  };

  // 验证优化后的设置数据
  validateOptimizedSettingsData = (settings) => {
    if (!settings || typeof settings !== 'object') return false;

    // 检查基本字段
    const hasBasicFields = 'evaluationCycle' in settings && 'selectedSubject' in settings;
    if (!hasBasicFields) return false;

    // 检查变更记录结构（如果存在）
    if (settings.statusChanges) {
      const { statusChanges } = settings;
      if (typeof statusChanges !== 'object') return false;

      // 检查statusChanges的结构
      if (statusChanges.scoreConfig && typeof statusChanges.scoreConfig !== 'object') return false;
      if (statusChanges.redlineRules && typeof statusChanges.redlineRules !== 'object') return false;
    }

    return true;
  };

  // 应用存储的变更（直接修改state数据，不触发事件处理）
  applyStoredChanges = (savedSettings) => {
    if (!savedSettings.statusChanges) return;

    const { statusChanges } = savedSettings;

    this.setState(prevState => {
      const newState = { ...prevState };
      const { evaluationCycle, selectedSubject } = newState;
      const currentCityData = newState.data[evaluationCycle];

      // 应用评分配置状态变更
      if (statusChanges.scoreConfig) {
        const updatedScoreConfigData = this.applyStatusChangesToData(
          currentCityData.scoreConfigData,
          statusChanges.scoreConfig
        );

        newState.data = {
          ...newState.data,
          [evaluationCycle]: {
            ...currentCityData,
            scoreConfigData: updatedScoreConfigData,
          },
        };
      }

      // 应用红线规则状态变更
      if (statusChanges.redlineRules) {
        const currentRules = currentCityData.redlineRules[selectedSubject] || [];
        const updatedRules = currentRules.map(rule => {
          if (statusChanges.redlineRules[rule.key] !== undefined) {
            return { ...rule, status: statusChanges.redlineRules[rule.key] };
          }
          return rule;
        });

        newState.data = {
          ...newState.data,
          [evaluationCycle]: {
            ...newState.data[evaluationCycle],
            redlineRules: {
              ...newState.data[evaluationCycle].redlineRules,
              [selectedSubject]: updatedRules,
            },
          },
        };
      }

      return newState;
    });
  };

  // 辅助方法：将状态变更应用到数据结构中
  applyStatusChangesToData = (dataArray, statusChanges) =>
    dataArray.map(item => {
      const updatedItem = { ...item };

      // 检查当前项是否需要更新
      if (statusChanges[item.key] !== undefined) {
        updatedItem.status = statusChanges[item.key];
      }

      // 递归处理子项
      if (item.children) {
        updatedItem.children = this.applyStatusChangesToData(item.children, statusChanges);
      }

      return updatedItem;
    });

  // 验证设置数据的完整性
  validateSettingsData = (settings) => {
    if (!settings || typeof settings !== 'object') return false;

    // 检查必要的字段
    const requiredFields = ['evaluationCycle', 'selectedSubject', 'data'];
    const hasAllFields = requiredFields.every(field => field in settings);
    if (!hasAllFields) return false;

    // 检查data结构
    if (!settings.data || typeof settings.data !== 'object') return false;

    // 检查每个城市的数据结构
    const cityKeys = Object.keys(settings.data);
    const isValidCityData = cityKeys.every(cityKey => {
      const cityData = settings.data[cityKey];
      return cityData.scoreConfigData && Array.isArray(cityData.scoreConfigData) &&
             cityData.redlineRules && typeof cityData.redlineRules === 'object';
    });

    return isValidCityData;
  };

  // 保存设置到localStorage（优化版 - 只存储关键设置）
  saveSettingsToStorage = () => {
    try {
      const { evaluationCycle, selectedSubject, expandedRowKeys } = this.state;

      // 只保存用户的选择和状态变更，不保存完整数据
      const settingsToSave = {
        evaluationCycle,
        selectedSubject,
        expandedRowKeys,
        // 只保存状态变更的记录
        statusChanges: this.getStatusChanges(),
        scoreChanges: this.getScoreChanges(),
        timestamp: new Date().toISOString(),
      };

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(settingsToSave));
      return true;
    } catch (error) {
      return false;
    }
  };

  // 获取状态变更记录（相对于默认值的变更）
  getStatusChanges = () => {
    const { data, evaluationCycle, selectedSubject } = this.state;
    const currentData = data[evaluationCycle];

    // 记录评分配置状态变更
    const scoreChanges = {};
    this.traverseScoreData(currentData.scoreConfigData, (item) => {
      // 假设默认状态都是true，只记录被禁用的项目
      if (item.status !== true) {
        scoreChanges[item.key] = item.status;
      }
    });

    // 记录红线规则状态变更
    const redlineChanges = {};
    const currentRules = currentData.redlineRules[selectedSubject] || [];
    currentRules.forEach(rule => {
      // 假设默认都是启用的，只记录被禁用的项目
      if (rule.status !== true) {
        redlineChanges[rule.key] = rule.status;
      }
    });

    return {
      scoreConfig: scoreChanges,
      redlineRules: redlineChanges,
    };
  };

  // 获取分数变更记录
  getScoreChanges = () => {
    // 这里可以记录分数的变更，如果有默认分数的话
    // 暂时返回空对象，因为当前分数都是固定的
    return {};
  };

  // 方案2：数据压缩存储（备选方案）
  // 使用简化的数据结构和键名缩写来减少存储空间
  saveCompressedSettingsToStorage = () => {
    try {
      const { evaluationCycle, selectedSubject, expandedRowKeys } = this.state;

      // 使用缩写键名减少存储空间
      const compressedSettings = {
        ec: evaluationCycle,           // evaluationCycle -> ec
        ss: selectedSubject,           // selectedSubject -> ss
        erk: expandedRowKeys,          // expandedRowKeys -> erk
        sc: this.getCompressedStatusChanges(), // statusChanges -> sc
        ts: Date.now(),                // timestamp -> ts (使用数字而不是ISO字符串)
      };

      // 进一步压缩：移除不必要的空格和换行
      const compressedString = JSON.stringify(compressedSettings);
      localStorage.setItem(this.STORAGE_KEY, compressedString);
      return true;
    } catch (error) {
      return false;
    }
  };

  // 获取压缩的状态变更记录
  getCompressedStatusChanges = () => {
    const { data, evaluationCycle, selectedSubject } = this.state;
    const currentData = data[evaluationCycle];
    const changes = {};

    // 只记录被禁用的项目（true是默认值，不需要存储）
    const disabledItems = [];

    // 收集被禁用的评分配置项
    this.traverseScoreData(currentData.scoreConfigData, (item) => {
      if (item.status === false) {
        disabledItems.push(item.key);
      }
    });

    // 收集被禁用的红线规则项
    const currentRules = currentData.redlineRules[selectedSubject] || [];
    const disabledRules = currentRules
      .filter(rule => rule.status === false)
      .map(rule => rule.key);

    // 只有在有禁用项时才添加到changes中
    if (disabledItems.length > 0) {
      changes.d_sc = disabledItems; // disabled_scoreConfig -> d_sc
    }
    if (disabledRules.length > 0) {
      changes.d_rl = disabledRules; // disabled_redlineRules -> d_rl
    }

    return changes;
  };

  // 加载压缩的设置数据
  loadCompressedSettingsFromStorage = () => {
    try {
      const savedSettings = localStorage.getItem(this.STORAGE_KEY);
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);

        // 检查是否是压缩格式
        if (parsedSettings.ec && parsedSettings.ss) {
          this.setState({
            evaluationCycle: parsedSettings.ec,
            selectedSubject: parsedSettings.ss,
          }, () => {
            this.applyCompressedChanges(parsedSettings.sc || {});
            // 设置默认展开第一项
            this.setDefaultExpanded();
          });
        }
      }
    } catch (error) {
      // 加载失败，使用默认设置
    }
  };

  // 应用压缩的变更记录（直接修改state数据）
  applyCompressedChanges = (compressedChanges) => {
    if (!compressedChanges) return;

    this.setState(prevState => {
      const newState = { ...prevState };
      const { evaluationCycle, selectedSubject } = newState;
      const currentCityData = newState.data[evaluationCycle];

      // 应用被禁用的评分配置项
      if (compressedChanges.d_sc && compressedChanges.d_sc.length > 0) {
        const statusChanges = {};
        compressedChanges.d_sc.forEach(key => {
          statusChanges[key] = false;
        });

        const updatedScoreConfigData = this.applyStatusChangesToData(
          currentCityData.scoreConfigData,
          statusChanges
        );

        newState.data = {
          ...newState.data,
          [evaluationCycle]: {
            ...currentCityData,
            scoreConfigData: updatedScoreConfigData,
          },
        };
      }

      // 应用被禁用的红线规则项
      if (compressedChanges.d_rl && compressedChanges.d_rl.length > 0) {
        const currentRules = currentCityData.redlineRules[selectedSubject] || [];
        const updatedRules = currentRules.map(rule => {
          if (compressedChanges.d_rl.includes(rule.key)) {
            return { ...rule, status: false };
          }
          return rule;
        });

        newState.data = {
          ...newState.data,
          [evaluationCycle]: {
            ...newState.data[evaluationCycle],
            redlineRules: {
              ...newState.data[evaluationCycle].redlineRules,
              [selectedSubject]: updatedRules,
            },
          },
        };
      }

      return newState;
    });
  };

  // 遍历评分数据的辅助方法
  traverseScoreData = (dataArray, callback) => {
    dataArray.forEach(item => {
      callback(item);
      if (item.children) {
        this.traverseScoreData(item.children, callback);
      }
    });
  };

  // 清除localStorage中的设置
  // clearSettingsFromStorage = () => {
  //   try {
  //     localStorage.removeItem(this.STORAGE_KEY);
  //     return true;
  //   } catch (error) {
  //     return false;
  //   }
  // };

  // 重置设置到默认值
  // resetToDefaultSettings = () => {
  //   this.clearSettingsFromStorage();
  //   // 重新加载页面或重置state到初始值
  //   window.location.reload();
  // };

  // 保存设置（智能选择存储方案）
  handleSave = () => {
    // 智能选择存储方案
    const success = this.saveOptimalSettings();

    if (success) {
      message.success('设置保存成功');
      router.push('/CaseQualityInspection');
    } else {
      message.error('设置保存失败，请重试');
    }

    // 这里也可以调用API保存数据到服务器
    // const { dispatch } = this.props;
    // dispatch({ type: 'caseQualitySetting/saveSettingData', payload: this.state });
  };

  // 智能选择最优存储方案
  saveOptimalSettings = () => {
    try {
      const changes = this.getStatusChanges();
      const changeCount = Object.keys(changes.scoreConfig || {}).length +
                         Object.keys(changes.redlineRules || {}).length;

      // 根据变更数量选择存储方案
      if (changeCount === 0) {
        // 没有变更，只保存基础设置
        return this.saveBasicSettingsOnly();
      }
      if (changeCount < 20) {
        // 变更较少，使用差异化存储（推荐方案）
        return this.saveSettingsToStorage();
      }
      // 变更较多，使用压缩存储
      return this.saveCompressedSettingsToStorage();
    } catch (error) {
      return false;
    }
  };

  // 只保存基础设置（无变更时）
  saveBasicSettingsOnly = () => {
    try {
      const { evaluationCycle, selectedSubject, expandedRowKeys } = this.state;
      const basicSettings = {
        evaluationCycle,
        selectedSubject,
        expandedRowKeys,
        timestamp: new Date().toISOString(),
      };

      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(basicSettings));
      return true;
    } catch (error) {
      return false;
    }
  };

  // 处理评估周期变化
  handleEvaluationCycleChange = value => {
    this.setState({
      evaluationCycle: value,
    });
  };

  // 处理学科选择变化
  handleSubjectChange = value => {
    this.setState({
      selectedSubject: value,
    });
  };

  // 处理评分配置状态变化（支持父子级联动）
  handleScoreStatusChange = (key, checked) => {
    const { data, evaluationCycle } = this.state;
    const currentCityData = data[evaluationCycle];

    // 检查是否为子项
    const isChildItem = key.includes('-');
    const parentKey = isChildItem ? key.split('-')[0] : key;

    const updateData = dataArray => {
      return dataArray.map(item => {
        if (item.key === key) {
          // 更新当前项状态
          const updatedItem = { ...item, status: checked };

          // 如果是父项，同时更新所有子项
          if (item.children && item.children.length > 0 && !isChildItem) {
            updatedItem.children = item.children.map(child => ({
              ...child,
              status: checked,
            }));
          }

          return updatedItem;
        }

        if (item.children) {
          const updatedChildren = updateData(item.children);

          // 如果当前项是父项，且有子项被更新，需要检查父项状态
          if (item.key === parentKey && isChildItem) {
            // 检查所有子项的状态来决定父项状态
            const anyChildEnabled = updatedChildren.some(child => child.status);

            return {
              ...item,
              children: updatedChildren,
              status: anyChildEnabled, // 有任何子项启用，父项就启用；全部禁用，父项就禁用
            };
          }

          return { ...item, children: updatedChildren };
        }

        return item;
      });
    };

    const updatedScoreConfigData = updateData(currentCityData.scoreConfigData);

    this.setState({
      data: {
        ...data,
        [evaluationCycle]: {
          ...currentCityData,
          scoreConfigData: updatedScoreConfigData,
        },
      },
    });
  };

  // 处理评分变化
  handleScoreChange = (key, value) => {
    const { data, evaluationCycle } = this.state;
    const currentCityData = data[evaluationCycle];
    const scoreConfigData = currentCityData.scoreConfigData;

    // 检查是否为子项
    const isChildItem = key.includes('-');

    if (isChildItem) {
      // 子项修改，需要验证总和
      const parentKey = key.split('-')[0];
      let parentItem = null;
      let childrenSum = 0;

      // 找到父项和计算子项总和
      const findParentAndSum = dataArray => {
        const parent = dataArray.find(item => item.key === parentKey);
        if (parent) {
          parentItem = parent;
          // 计算修改后的子项总和
          childrenSum = parent.children.reduce((sum, child) => {
            if (child.key === key) {
              return sum + (value || 0);
            }
            return sum + (child.score || 0);
          }, 0);
        }
      };

      findParentAndSum(scoreConfigData);

      // 验证子项总和是否等于父项分数
      if (parentItem && childrenSum !== parentItem.score) {
        // 可以在这里添加提示信息或阻止修改
        // 暂时允许修改，但可以在UI上显示警告
      }
    }

    const updateData = dataArray => {
      return dataArray.map(item => {
        if (item.key === key) {
          return { ...item, score: value };
        }
        if (item.children) {
          return { ...item, children: updateData(item.children) };
        }
        return item;
      });
    };

    const updatedScoreConfigData = updateData(scoreConfigData);

    this.setState({
      data: {
        ...data,
        [evaluationCycle]: {
          ...currentCityData,
          scoreConfigData: updatedScoreConfigData,
        },
      },
    });
  };

  // 处理红线规则状态变化
  handleRedlineStatusChange = (key, checked) => {
    const { data, evaluationCycle, selectedSubject } = this.state;
    const currentCityData = data[evaluationCycle];
    const redlineRules = currentCityData.redlineRules || {};

    // 更新当前学科的规则状态
    const currentRules = redlineRules[selectedSubject] || [];
    const updatedRules = currentRules.map(rule => {
      if (rule.key === key) {
        return { ...rule, status: checked };
      }
      return rule;
    });

    // 更新整个redlineRules对象
    const newRedlineRules = {
      ...redlineRules,
      [selectedSubject]: updatedRules,
    };

    this.setState({
      data: {
        ...data,
        [evaluationCycle]: {
          ...currentCityData,
          redlineRules: newRedlineRules,
        },
      },
    });
  };

  // 返回上一页
  handleGoBack = () => {
    router.push('/CaseQualityInspection');
  };

  // 获取当前城市的数据
  getCurrentCityData = () => {
    const { data, evaluationCycle } = this.state;
    return data[evaluationCycle] || { scoreConfigData: [], redlineRules: {} };
  };

  // 获取当前城市的质检评分配置数据
  getCurrentScoreConfigData = () => {
    const currentData = this.getCurrentCityData();
    return currentData.scoreConfigData || [];
  };

  // 获取当前城市和学科的红线规则数据
  getCurrentRedlineRules = () => {
    const { selectedSubject } = this.state;
    const currentData = this.getCurrentCityData();
    const redlineRules = currentData.redlineRules || {};
    return redlineRules[selectedSubject] || [];
  };

  // 质检评分配置表格列定义
  getScoreConfigColumns = () => {
    const { expandedRowKeys } = this.state;

    return [
      {
        title: '模块',
        dataIndex: 'category',
        key: 'category',
        width: 200,
        render: (text, record) => {
          const hasChildren = record.children && record.children.length > 0;
          const isExpanded = expandedRowKeys.includes(record.key);

          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              {hasChildren && (
                <span
                  style={{
                    cursor: 'pointer',
                    color: '#1890ff',
                    fontSize: '12px',
                    marginRight: 8,
                    userSelect: 'none',
                    display: 'inline-block',
                    width: '16px',
                    textAlign: 'center',
                  }}
                  onClick={e => {
                    e.stopPropagation();
                    this.handleExpand(!isExpanded, record);
                  }}
                >
                  {isExpanded ? '▼' : '▶'}
                </span>
              )}
              {!hasChildren && <span style={{ width: '24px', display: 'inline-block' }} />}
              <span>{text || ''}</span>
            </div>
          );
        },
      },
      {
        title: '质检项目',
        dataIndex: 'ruleDescription',
        key: 'ruleDescription',
        render: text => text,
      },
      {
        title: '扣分标准',
        dataIndex: 'score',
        key: 'score',
        width: 200,
        render: (text, record) => {
          // 判断是否为子项（第二级）
          const isChildItem = record.key.includes('-');

          if (isChildItem) {
            // 子项不显示扣分标准数值
            return null;
          }
          // 父项显示扣分标准数值
          return <span>{text}</span>;
        },
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 200,
        render: (text, record) => (
          <div
            className={styles.switchWrapper}
            onClick={e => e.stopPropagation()} // 阻止事件冒泡到行点击
          >
            <Switch
              checked={text}
              onChange={checked => this.handleScoreStatusChange(record.key, checked)}
              size="large"
            />
            <span className={styles.switchText}>{text ? '启用' : '禁用'}</span>
          </div>
        ),
      },
    ];
  };

  // 处理表格展开变化
  handleExpand = (expanded, record) => {
    let newExpandedRowKeys;

    if (expanded) {
      // 展开当前行，同时收起其他所有行（只允许展开一个）
      newExpandedRowKeys = [record.key];
    } else {
      // 收起当前行
      newExpandedRowKeys = [];
    }

    this.setState({
      expandedRowKeys: newExpandedRowKeys,
    });
  };

  // 红线规则表格列定义
  getRedlineRulesColumns = () => [
    {
      title: '红线规则',
      dataIndex: 'rule',
      key: 'rule',
      render: text => text, // 使用CSS统一样式，移除内联样式
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 200,
      align: 'left',
      render: (status, record) => (
        <div className={styles.switchWrapper}>
          <Switch
            checked={status}
            onChange={checked => this.handleRedlineStatusChange(record.key, checked)}
            size="large"
          />
          <span className={styles.switchText}>{status ? '启用' : '禁用'}</span>
        </div>
      ),
    },
  ];

  render() {
    const { evaluationCycle, selectedSubject, expandedRowKeys } = this.state;

    return (
      <div className={styles.settingContainer}>
        <div className={styles.goback} onClick={this.handleGoBack}>
          <img src={backIcon} alt="" />
          <span>返回上一页</span>
        </div>
        <div className={styles.header}>
          <h2>设置</h2>
          <Button type="primary" onClick={this.handleSave}>
            保存设置
          </Button>
        </div>

        {/* 基础设置 */}
        <div className={styles.settingCard}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>基础设置</h3>
            <span className={styles.cardDes}>配置基础评分规则</span>
          </div>
          <div className={styles.cardBody}>
            <div className={styles.settingContent}>
              <span className={styles.fieldLabel}>评分规则：</span>
              <Select
                value={evaluationCycle}
                onChange={this.handleEvaluationCycleChange}
                style={{ width: 240 }}
              >
                <Option value="beijing">北京市标准</Option>
                {/* <Option value="shanghai">上海市标准</Option> */}
              </Select>
            </div>
          </div>
        </div>

        {/* 质检评分配置 */}
        <div className={styles.settingCard}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>质检评分配置</h3>
            <span className={styles.cardDes}>配置各项质检的评分标准和扣分规则</span>
          </div>
          <div className={styles.cardBody}>
            <div className={styles.tableWrapper}>
              <Table
                columns={this.getScoreConfigColumns()}
                dataSource={this.getCurrentScoreConfigData()}
                pagination={false}
                size="middle"
                rowKey="key"
                className={styles.scoreConfigTable}
                expandedRowKeys={expandedRowKeys}
                onExpand={this.handleExpand}
                onRow={record => ({
                  onClick: event => {
                    // 只有父级行（有children的行）才处理点击事件
                    if (record.children && record.children.length > 0) {
                      // 阻止事件冒泡，避免与其他点击事件冲突
                      event.stopPropagation();
                      const isExpanded = expandedRowKeys.includes(record.key);
                      this.handleExpand(!isExpanded, record);
                    }
                  },
                  style: {
                    cursor: record.children && record.children.length > 0 ? 'pointer' : 'default',
                  },
                })}
              />
            </div>
          </div>
        </div>

        {/* 学科红线规则 */}
        <div className={styles.settingCard}>
          <div className={styles.cardHeader}>
            <h3 className={styles.cardTitle}>学科红线规则</h3>
            <span className={styles.cardDes}>为不同学科配置特定的红线质检规则</span>
          </div>
          <div className={styles.cardBody}>
            <div className={styles.redlineContent}>
              <span className={styles.fieldLabel}>选择学科：</span>
              <Select
                value={selectedSubject}
                onChange={this.handleSubjectChange}
                style={{ width: 240 }}
              >
                <Option value="orthodontics">正畸</Option>
                <Option value="planting">种植</Option>
                <Option value="rootCanal">根管治疗</Option>
                <Option value="periodontal">牙周治疗</Option>
                <Option value="surgical">外科（拔牙）</Option>
                <Option value="repair">固定修复</Option>
              </Select>
            </div>

            <div className={styles.tableWrapper}>
              <Table
                columns={this.getRedlineRulesColumns()}
                dataSource={this.getCurrentRedlineRules()}
                pagination={false}
                size="middle"
                rowKey="key"
                className={styles.redlineRulesTable}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default CaseQualitySetting;
